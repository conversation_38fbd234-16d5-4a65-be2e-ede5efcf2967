import { AuthUser, Permissions, PermissionsGuard } from "@microservices/auth";
import { TakeHomeSubmissionService, ReviewAndScoreService } from "@microservices/db";
import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
  Logger,
  ParseIntPipe,
} from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiParam, ApiQuery } from "@nestjs/swagger";

export interface UpdateSubmissionFeedbackDto {
  feedback: string;
  totalScore?: number;
}

export interface CreateReviewDto {
  review: string;
  score: string;
  userId: number;
  assessmentId: number;
  assessmentType: 'Coding Assessment';
  duration: string;
  completedOn: string;
  userFlowStatus: string;
  assessmentStatus: string;
}

@Controller('take-home-submissions')
export class TakeHomeSubmissionController {
  constructor(
    private takeHomeSubmissionService: TakeHomeSubmissionService,
    private reviewAndScoreService: ReviewAndScoreService
  ) {}

  @ApiOperation({ summary: "Get take-home submission by candidate and job" })
  @ApiResponse({ status: 200 })
  @ApiBearerAuth("access-token")
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/candidate/:candidateId/job/:jobId")
  @Permissions("recruiter", "assessment:view")
  async getSubmissionByCandidate(
    @Param("candidateId", ParseIntPipe) candidateId: number,
    @Param("jobId", ParseIntPipe) jobId: number
  ) {
    try {
      const submission = await this.takeHomeSubmissionService.findByCandidate(candidateId, jobId);
      if (!submission) {
        return { message: "No submission found for this candidate and job" };
      }
      return submission;
    } catch (error) {
      Logger.error(`Error fetching submission: ${error.message}`, error.stack);
      throw error;
    }
  }

  @ApiOperation({ summary: "Get take-home submission by ID" })
  @ApiResponse({ status: 200 })
  @ApiBearerAuth("access-token")
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/:id")
  @Permissions("recruiter", "assessment:view")
  async getSubmissionById(@Param("id", ParseIntPipe) id: number) {
    try {
      return await this.takeHomeSubmissionService.findById(id);
    } catch (error) {
      Logger.error(`Error fetching submission by ID: ${error.message}`, error.stack);
      throw error;
    }
  }

  @ApiOperation({ summary: "Get all take-home submissions for a company" })
  @ApiResponse({ status: 200 })
  @ApiBearerAuth("access-token")
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get()
  @Permissions("recruiter", "assessment:view")
  async getAllSubmissions(@AuthUser() user: any) {
    try {
      const companyId = user["https://urecruits.com/companyId"];
      return await this.takeHomeSubmissionService.findAll(companyId);
    } catch (error) {
      Logger.error(`Error fetching all submissions: ${error.message}`, error.stack);
      throw error;
    }
  }

  @ApiOperation({ summary: "Update submission feedback and score" })
  @ApiResponse({ status: 200 })
  @ApiBearerAuth("access-token")
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch("/:id/feedback")
  @Permissions("recruiter", "assessment:edit")
  async updateFeedback(
    @Param("id", ParseIntPipe) id: number,
    @Body() dto: UpdateSubmissionFeedbackDto
  ) {
    try {
      return await this.takeHomeSubmissionService.updateFeedback(
        id,
        dto.feedback,
        dto.totalScore
      );
    } catch (error) {
      Logger.error(`Error updating feedback: ${error.message}`, error.stack);
      throw error;
    }
  }

  @ApiOperation({ summary: "Create review and score record for take-home submission" })
  @ApiResponse({ status: 201 })
  @ApiBearerAuth("access-token")
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post("/:id/review")
  @Permissions("recruiter", "assessment:edit")
  async createReview(
    @Param("id", ParseIntPipe) submissionId: number,
    @Body() dto: CreateReviewDto
  ) {
    try {
      // First, get the submission to validate it exists
      const submission = await this.takeHomeSubmissionService.findById(submissionId);
      
      // Create the review record
      const reviewData = {
        ...dto,
        assessmentType: 'Coding Assessment' as any,
      };
      
      const review = await this.reviewAndScoreService.create(reviewData as any);
      
      // Also update the submission with the feedback
      if (dto.review) {
        await this.takeHomeSubmissionService.updateFeedback(
          submissionId,
          dto.review,
          dto.score ? parseInt(dto.score) : undefined
        );
      }
      
      return review;
    } catch (error) {
      Logger.error(`Error creating review: ${error.message}`, error.stack);
      throw error;
    }
  }

  @ApiOperation({ summary: "Get submission with detailed code and test results for review" })
  @ApiResponse({ status: 200 })
  @ApiBearerAuth("access-token")
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/:id/review-details")
  @Permissions("recruiter", "assessment:view")
  async getSubmissionForReview(@Param("id", ParseIntPipe) id: number) {
    try {
      const submission = await this.takeHomeSubmissionService.findById(id);
      
      // Format the response for easy review
      const reviewData = {
        submissionId: submission.id,
        candidateId: submission.candidateId,
        jobId: submission.jobId,
        assessmentId: submission.assessmentId,
        duration: submission.duration,
        submittedAt: submission.submittedAt,
        language: submission.language,
        totalScore: submission.totalScore,
        feedback: submission.feedback,
        testCaseSummary: submission.testCaseSummary,
        takeHomeTask: submission.takeHomeTask,
        questionSubmissions: submission.questionSubmissions.map((q: any) => ({
          questionId: q.questionId,
          questionName: q.questionName,
          finalCode: q.finalCode,
          testCaseResults: q.testCaseResults,
          executionSummary: q.executionSummary,
          passedTests: q.testCaseResults?.filter((tc: any) => tc.passed).length || 0,
          totalTests: q.testCaseResults?.length || 0,
          successRate: q.testCaseResults?.length > 0 
            ? Math.round((q.testCaseResults.filter((tc: any) => tc.passed).length / q.testCaseResults.length) * 100)
            : 0
        }))
      };
      
      return reviewData;
    } catch (error) {
      Logger.error(`Error fetching submission for review: ${error.message}`, error.stack);
      throw error;
    }
  }
}
