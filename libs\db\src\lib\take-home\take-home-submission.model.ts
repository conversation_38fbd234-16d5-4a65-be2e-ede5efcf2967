import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON>T<PERSON>, <PERSON><PERSON>ey, <PERSON><PERSON>any, Model, Table } from "sequelize-typescript";
import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsInt, IsString, <PERSON>Optional, IsJSON } from "class-validator";
import { TakeHomeTask } from "./take-home-task.model";

@Table({
  tableName: "take-home-submissions",
  createdAt: true,
  updatedAt: true,
})
export class TakeHomeSubmission extends Model<TakeHomeSubmission> {
  @ApiProperty({ example: "1", description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @IsString()
  @ApiProperty({
    example: "45:30",
    description: "Duration taken to complete the assessment",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  duration: string;

  @IsOptional()
  @ApiProperty({
    example: "85",
    description: "Total score achieved",
  })
  @Column({ type: DataType.INTEGER, allowNull: true })
  totalScore?: number;

  @ApiProperty({
    example: "1",
    description: "Job id",
  })
  @Column({ type: DataType.INTEGER, allowNull: false })
  jobId: number;

  @ApiProperty({
    example: "1",
    description: "Candidate id",
  })
  @Column({ type: DataType.INTEGER, allowNull: false })
  candidateId: number;

  @ApiProperty({
    example: "1",
    description: "Take Home Task Assessment Id",
  })
  @ForeignKey(() => TakeHomeTask)
  @Column({ type: DataType.INTEGER, allowNull: false })
  assessmentId: number;

  @BelongsTo(() => TakeHomeTask)
  takeHomeTask: TakeHomeTask;

  @ApiProperty({
    example: "1",
    description: "Assignment id for linking to coding area data",
  })
  @Column({ type: DataType.INTEGER, allowNull: true })
  assignmentId: number;

  @IsJSON()
  @ApiProperty({
    example: "[]",
    description: "Array of question submissions with code and test results",
  })
  @Column({ type: DataType.JSONB, allowNull: false })
  questionSubmissions: any[];

  @IsOptional()
  @ApiProperty({
    example: "Great solution with clean code structure",
    description: "Reviewer feedback",
  })
  @Column({ type: DataType.TEXT, allowNull: true })
  feedback?: string;

  @IsOptional()
  @ApiProperty({
    example: "2023-12-01T10:30:00Z",
    description: "Timestamp when assessment was submitted",
  })
  @Column({ type: DataType.DATE, allowNull: true })
  submittedAt?: Date;

  @IsOptional()
  @ApiProperty({
    example: "javascript",
    description: "Programming language used",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  language?: string;

  @IsJSON()
  @ApiProperty({
    example: "{}",
    description: "Summary of test case results",
  })
  @Column({ type: DataType.JSONB, allowNull: true })
  testCaseSummary?: any;
}
