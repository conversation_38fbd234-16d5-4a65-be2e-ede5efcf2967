import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { TakeHomeSubmission } from './take-home-submission.model';
import { TakeHomeSubmissionService } from './take-home-submission.service';
import { CodingAreaModule } from '../coding-area/coding-area.module';
import { AssignmentModule } from '../assignment/assignment.module';

@Module({
  imports: [
    SequelizeModule.forFeature([TakeHomeSubmission]),
    CodingAreaModule,
    AssignmentModule,
  ],
  providers: [TakeHomeSubmissionService],
  exports: [TakeHomeSubmissionService, SequelizeModule],
})
export class TakeHomeSubmissionModule {}
