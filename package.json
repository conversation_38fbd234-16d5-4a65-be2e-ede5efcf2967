{"name": "microservices", "version": "0.0.0", "license": "MIT", "scripts": {"start": "nx serve", "start:assessment": "nx serve assessment-api", "start:pre": "nx serve pre-recruitment-api", "start:hr": "nx serve hr-analytics-api", "start:asses": "nx serve assessment-api", "start:integrations": "nx serve integrations-api", "start:all": "NODE_OPTIONS='--max-old-space-size=8192' nx run-many --target=serve --projects=hr-analytics-api,pre-recruitment-api,assessment-api,integrations-api,temporal", "format": "nx format:write", "lint": "nx affected:lint --all --fix", "build": "nx run-many --target=build --projects=pre-recruitment-api,assessment-api,integrations-api,temporal,hr-analytics-api --parallel", "build:workflow": "ts-node apps/temporal/src/app/workflow/temporal/built-workflow-bundle.ts", "build-assessment": "nx build assessment-api", "build-pre-rec": "nx build pre-recruitment-api", "build-integrations": "nx build integrations-api", "build-hr": "nx build hr-analytics-api", "test": "nx test", "temporal": "temporal server start-dev --ui-port 8080", "db:setup": "docker run -d -p 9998:5432 --name urecruits-db -e POSTGRES_PASSWORD=test -e POSTGRES_USER=test -e POSTGRES_DB=urecruits postgres", "redis": "docker run -d -p 6379:6379 --name urecruits-redis redis"}, "private": true, "dependencies": {"@azure/msal-node": "3.6.2", "@convergence/jwt-util": "^0.2.0", "@dropbox/sign": "^1.3.0", "@google-cloud/local-auth": "^2.1.0", "@google-cloud/talent": "^4.1.1", "@langchain/core": "^0.3.57", "@langchain/langgraph": "^0.3.0", "@langchain/openai": "^0.0.28", "@microsoft/microsoft-graph-client": "^3.0.4", "@nestjs/axios": "^3.0.0", "@nestjs/common": "^8.0.0", "@nestjs/config": "^1.1.6", "@nestjs/core": "^8.0.0", "@nestjs/microservices": "^8.2.4", "@nestjs/passport": "^9.0.0", "@nestjs/platform-express": "^8.0.0", "@nestjs/platform-socket.io": "^8.2.5", "@nestjs/sequelize": "^8.0.0", "@nestjs/swagger": "^5.2.0", "@nestjs/websockets": "^8.2.5", "@opensearch-project/opensearch": "^2.5.0", "@qdrant/qdrant-js": "^1.13.0", "@sendgrid/mail": "^7.6.0", "@temporalio/activity": "^1.11.2", "@temporalio/client": "^1.11.2", "@temporalio/common": "^1.11.2", "@temporalio/worker": "^1.11.2", "@temporalio/workflow": "^1.11.2", "@types/archiver": "^5.3.1", "@types/base-64": "^1.0.0", "@types/multer": "^1.4.7", "archiver": "^5.3.1", "aws-sdk": "2.1059.0", "base-64": "^1.0.0", "bcrypt": "^5.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "cross-fetch": "^3.1.5", "dotenv": "^16.4.7", "express-jwt": "^6.1.0", "firebase": "^10.12.2", "firebase-admin": "^12.1.1", "googleapis": "^107.0.0", "isomorphic-fetch": "^3.0.0", "jwks-rsa": "^2.0.5", "langchain": "^0.1.36", "langgraph": "langchain-ai/langgraph", "moment": "^2.29.4", "multer": "1.4.4", "multer-s3": "2.10.0", "mysql2": "^2.3.3", "nestjs-stripe": "1.0.0", "nodemailer": "^6.7.2", "passport": "^0.6.0", "passport-jwt": "^4.0.0", "pdf-parse": "^1.1.1", "pg": "^8.11.5", "puppeteer": "^22.6.2", "redis": "^3", "reflect-metadata": "^0.1.13", "rxjs": "^7.0.0", "sequelize": "^6.25.3", "sequelize-typescript": "^2.1.5", "sqlite3": "^5.1.1", "stripe": "^8.222.0", "swagger-ui-express": "^4.3.0", "ts-node": "^10.9.2", "tslib": "^2.0.0", "twilio": "^3.81.0", "undici": "^5.5.1", "uuid": "^9.0.0", "zod": "^3.23.5"}, "devDependencies": {"@macpaw/eslint-config-base": "^2.0.1", "@macpaw/eslint-config-typescript": "^2.0.1", "@microsoft/microsoft-graph-types": "^2.25.0", "@nestjs/schematics": "^8.0.0", "@nestjs/testing": "^8.0.0", "@nrwl/cli": "13.4.3", "@nrwl/eslint-plugin-nx": "13.4.3", "@nrwl/jest": "13.4.3", "@nrwl/linter": "13.4.3", "@nrwl/nest": "13.4.3", "@nrwl/node": "13.4.3", "@nrwl/tao": "13.4.3", "@nrwl/workspace": "13.4.3", "@types/jest": "27.0.2", "@types/node": "14.14.33", "@types/nodemailer": "^6.4.4", "@types/passport-jwt": "^3.0.6", "@types/sequelize": "^4.28.14", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "eslint": "8.57.1", "eslint-config-prettier": "8.1.0", "husky": "^7.0.4", "jest": "30.0.4", "lint-staged": "^12.3.1", "prettier": "^2.3.1", "ts-jest": "27.0.5", "typescript": "^5.6.3"}}