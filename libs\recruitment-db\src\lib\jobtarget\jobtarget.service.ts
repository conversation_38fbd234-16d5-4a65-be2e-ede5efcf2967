import { Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { JobTargetIntegration } from './jobtarget.model';
import { JobTargetIntegrationDto } from './jobtarget.dto';
import { Jobs } from '../job/job.model';
import { Company } from '../companies/companies.model';
import { User } from '../users/users.model';
import axios from 'axios';
import { string } from 'zod';

@Injectable()
export class JobTargetService {
    constructor(
        @InjectModel(JobTargetIntegration)
        private readonly jobTargetModel: typeof JobTargetIntegration,

        @InjectModel(Company)
        private readonly companyModel: typeof Company,

        @InjectModel(User)
        private readonly userModel: typeof User,

        @InjectModel(Jobs)
        private readonly jobModel: typeof Jobs,
    ) { }

    async integrateAccount(companyId: number, userId: number) {
        try {
            // Step 1: Get OAuth token
            let token: string;
            try {
                const clientId = process.env.JOBTARGET_CLIENT_ID;
                const clientSecret = process.env.JOBTARGET_CLIENT_SECRET;

                const response = await axios.post(
                    'https://uat-partner-api.jobtarget.com/api/token',
                    {
                        client_id: clientId,
                        client_secret: clientSecret,
                        grant_type: 'client_credentials',
                    },
                    {
                        headers: { 'Content-Type': 'application/json' },
                    },
                );

                token = response.data?.token;

                if (!token) {
                    throw new Error('No token received from JobTarget token API.');
                }

                // Save token to JobTargetIntegration table right after receiving it
                await this.jobTargetModel.create({
                    companyId,
                    token,
                });

            } catch (error) {
                throw new Error(`Failed to fetch JobTarget token: ${error.message}`);
            }

            let integration: any;

            // Step 3: Handle company integration
            const company = await this.companyModel.findByPk(companyId);
            if (!company) {
                throw new Error(`Company with ID ${companyId} not found.`);
            }

            let jobTargetCompanyId = company.jobTargetId;
            const firstAddress = company.addresses?.[0];

            if (!jobTargetCompanyId) {
                const createCompanyPayload = {
                    externalCompanyId: company.id.toString(),
                    name: company.name,
                    demo: false,
                    accountType: 'enterprise',
                    location: {
                        city: firstAddress?.city || '',
                        state: firstAddress?.location || '',
                        country: firstAddress?.country || '',
                        zip: firstAddress?.zip || '',
                    },
                    metadata: {
                        linkedinExternalId: '',
                    },
                };

                try {
                    const createCompanyResponse = await axios.post(
                        'https://uat-partner-api.jobtarget.com/api/v1/company',
                        createCompanyPayload,
                        {
                            headers: {
                                'Content-Type': 'application/json',
                                Authorization: `Bearer ${token}`,
                            },
                            validateStatus: (status) => status === 200 || status === 201,
                        },
                    );

                    jobTargetCompanyId = createCompanyResponse.data?.companyId;
                    if (!jobTargetCompanyId) {
                        throw new Error('JobTarget company creation did not return a companyId.');
                    }

                    company.jobTargetId = jobTargetCompanyId;
                    await company.save();
                } catch (error) {
                    throw new Error(`Failed to create JobTarget company: ${error.message}`);
                }
            }

            // Step 4: Handle user integration
            const user = await this.userModel.findByPk(userId);
            if (!user) {
                throw new Error(`User with ID ${userId} not found.`);
            }

            if (!user.jobTargetId) {
                const createUserPayload = {
                    externalUserId: user.id.toString(),
                    firstName: user.firstname,
                    lastName: user.lastname,
                    title: 'Recruiter',
                    email: user.email,
                    phoneNumber: user.phone,
                    isAdmin: true,
                    companyId: jobTargetCompanyId,
                };

                try {
                    const createUserResponse = await axios.post(
                        `https://uat-partner-api.jobtarget.com/api/v1/company/${jobTargetCompanyId}/user`,
                        createUserPayload,
                        {
                            headers: {
                                'Content-Type': 'application/json',
                                Authorization: `Bearer ${token}`,
                            },
                            validateStatus: (status) => status === 200 || status === 201,
                        }
                    );

                    const jobTargetUserId = createUserResponse.data?.userId;
                    if (!jobTargetUserId) {
                        throw new Error('JobTarget user creation did not return a userId.');
                    }

                    user.jobTargetId = jobTargetUserId;
                    await user.save();
                } catch (error) {
                    throw new Error(`Failed to create JobTarget user: ${error.message}`);
                }
            }
            return integration;
        } catch (error) {
            console.error('JobTarget integration failed:', error.message);
            throw new InternalServerErrorException(error.message || 'JobTarget integration error.');
        }
    }


    async removeJobTargetAccount(companyId: number) {
        try {
            return await this.jobTargetModel.destroy({
                where: {
                    companyId,
                },
            });
        } catch (error) {
            Logger.log(
                "Error generated during removing record from jobtarget-integration table",
                error
            );
        }
    }

    async getJobIntegrationDetails(companyId: number) {
        try {
            const response = await this.jobTargetModel.findOne({
                where: { companyId },
                attributes: ["token"],
            });
            Logger.log(`JobTarget integration details retrieved for company ${companyId}:`, !!response);
            return response;
        } catch (error) {
            Logger.log(
                "Error generated during getting record from jobtarget-integration table",
                error
            );
            throw error;
        }
    }

    async fetchJobTargetToken(userEmail: string): Promise<string> {
        const clientId = process.env.JOBTARGET_CLIENT_ID;
        const clientSecret = process.env.JOBTARGET_CLIENT_SECRET;

        try {
            const response = await axios.post(
                'https://uat-partner-api.jobtarget.com/api/token',
                {
                    client_id: clientId,
                    client_secret: clientSecret,
                    grant_type: 'user_credentials',
                    email: userEmail,
                },
                {
                    headers: { 'Content-Type': 'application/json' },
                }
            );

            const token = response.data?.token;

            if (!token) {
                throw new Error('No token received from JobTarget token API.');
            }
            return token;
        } catch (error) {
            Logger.error('Failed to fetch JobTarget token', error.response?.data || error.message);

            if (
                error.response?.status === 400 &&
                error.response?.data?.message?.toLowerCase().includes('email')
            ) {
                Logger.warn(`JobTarget user email issue: ${userEmail} may not exist on JobTarget.`);
            }

            throw new Error('Unable to fetch JobTarget token');
        }
    }

    async createJob(job: any) {
        let jobPayload: any = null;

        try {
            const companyId = job.companyId;
            const authorId = job.authorId;

            // Get integration credentials (client ID/secret)
            const integration = await this.getJobIntegrationDetails(companyId);
            if (!integration) {
                Logger.warn(`No JobTarget integration found for company ${companyId}`);
                return null;
            }

            // Get company & user
            const company = await this.companyModel.findByPk(companyId);
            const user = await this.userModel.findByPk(authorId);

            if (!company?.name || !user?.email) {
                Logger.warn(`Missing company name or user email for company ${companyId}`);
                return null;
            }

            // Fetch fresh token
            const token = await this.fetchJobTargetToken(user.email);
            const webAppsUrl = process.env.WEB_APPS_URL || 'http://localhost:9000';

            // Helper function to map experience values to JobTarget valid reference names
            const mapExperience = (experienceMin: number, experienceMax: number) => {
                if (!experienceMin && !experienceMax) return 'None';
                if (experienceMin === 0 && experienceMax === 0) return 'None';

                const maxExp = experienceMax || experienceMin || 0;
                const minExp = experienceMin || 0;

                if (maxExp <= 1) return 'Years0_1';
                if (maxExp <= 2) return 'Years1_2';
                if (maxExp <= 3) return 'Years2_3';
                if (maxExp <= 5) return 'Years3_5';
                if (maxExp <= 7) return 'Years5_7';
                if (maxExp <= 10) return 'Years7_10';

                return 'YearsOver10';
            };

            // Helper function to map job function to JobTarget valid reference names
            const mapJobFunction = (functionalArea: string, positionLabel: string) => {
                const area = functionalArea || positionLabel || '';
                const areaLower = area.toLowerCase();

                if (areaLower.includes('accounting') || areaLower.includes('finance')) return 'Accounting_Finance';
                if (areaLower.includes('admin') || areaLower.includes('administrative') || areaLower.includes('clerical') || areaLower.includes('secretarial')) return 'Admin_Clerical_Secretarial';
                if (areaLower.includes('business development') || areaLower.includes('bizdev')) return 'BusinessDevelopment';
                if (areaLower.includes('consultant') || areaLower.includes('consulting')) return 'Consultant';
                if (areaLower.includes('customer') || areaLower.includes('service')) return 'CustomerService';
                if (areaLower.includes('design') || areaLower.includes('designer')) return 'Design';
                if (areaLower.includes('engineer') || areaLower.includes('engineering')) return 'Engineering';
                if (areaLower.includes('executive') || areaLower.includes('management') || areaLower.includes('manager') || areaLower.includes('director') || areaLower.includes('ceo') || areaLower.includes('cto') || areaLower.includes('cfo')) return 'Executive_Management';
                if (areaLower.includes('facilities') || areaLower.includes('facility')) return 'Facitilies';
                if (areaLower.includes('general business') || areaLower.includes('business')) return 'GeneralBusiness';
                if (areaLower.includes('hr') || areaLower.includes('human resources') || areaLower.includes('human')) return 'HumanResources';
                if (areaLower.includes('it') || areaLower.includes('information technology') || areaLower.includes('tech') || areaLower.includes('software') || areaLower.includes('developer')) return 'InformationTechnology';
                if (areaLower.includes('inventory') || areaLower.includes('warehouse')) return 'Inventory';
                if (areaLower.includes('manufacturing') || areaLower.includes('production')) return 'Manufacturing';
                if (areaLower.includes('marketing') || areaLower.includes('digital marketing')) return 'Marketing';
                if (areaLower.includes('medical') || areaLower.includes('healthcare') || areaLower.includes('nurse') || areaLower.includes('doctor')) return 'Medical';
                if (areaLower.includes('professional services') || areaLower.includes('legal') || areaLower.includes('law')) return 'ProfessionalServices';
                if (areaLower.includes('purchasing') || areaLower.includes('procurement')) return 'Purchasing';
                if (areaLower.includes('quality') || areaLower.includes('qa') || areaLower.includes('testing')) return 'QualityAssurance';
                if (areaLower.includes('research') || areaLower.includes('r&d')) return 'Research';
                if (areaLower.includes('safety') || areaLower.includes('security')) return 'Safety';
                if (areaLower.includes('sales') || areaLower.includes('selling')) return 'Sales';
                if (areaLower.includes('science') || areaLower.includes('scientist')) return 'Science';
                if (areaLower.includes('shipping') || areaLower.includes('logistics')) return 'Shipping';
                if (areaLower.includes('skilled labor') || areaLower.includes('labor') || areaLower.includes('technician')) return 'SkilledLabor';
                if (areaLower.includes('strategy') || areaLower.includes('planning') || areaLower.includes('analyst')) return 'Strategy_Planning';
                if (areaLower.includes('supply chain') || areaLower.includes('supply')) return 'SupplyChain';

                return 'Other';
            };

            const mapIndustry = (industryLabel: string) => {
                if (!industryLabel) return 'General';
                const industryLower = industryLabel.toLowerCase();

                // Map to exact JobTarget industry reference names
                if (industryLower.includes('advertising') || industryLower.includes('marketing')) return 'Advertising_Marketing';
                if (industryLower.includes('agricultural') || industryLower.includes('agriculture') || industryLower.includes('farming')) return 'Agricultural';
                if (industryLower.includes('airline') || industryLower.includes('aerospace') || industryLower.includes('aviation')) return 'Airline_Aerospace_Aviation';
                if (industryLower.includes('apparel') || industryLower.includes('textile') || industryLower.includes('clothing')) return 'Apparel_Textiles';
                if (industryLower.includes('architecture') || industryLower.includes('design')) return 'Architecture_Design';
                if (industryLower.includes('art') || industryLower.includes('photography')) return 'Art_Photography';
                if (industryLower.includes('automotive') || industryLower.includes('vehicle') || industryLower.includes('car')) return 'Automotive_Vehicles_Parts_Service';
                if (industryLower.includes('banking') || industryLower.includes('financial') || industryLower.includes('accounting')) return 'Banking_Accounting_Financial';
                if (industryLower.includes('biotechnology') || industryLower.includes('biotech')) return 'Biotechnology';
                if (industryLower.includes('broadcasting') || industryLower.includes('radio') || industryLower.includes('television') || industryLower.includes('tv')) return 'Broadcasting_Radio_TV';
                if (industryLower.includes('building materials')) return 'BuildingMaterials';
                if (industryLower.includes('computer hardware') || industryLower.includes('hardware')) return 'ComputerHardware';
                if (industryLower.includes('computer software') || industryLower.includes('software') || industryLower.includes('technology') || industryLower.includes('information technology') || industryLower.includes('it')) return 'ComputerSoftware';
                if (industryLower.includes('construction')) return 'Construction';
                if (industryLower.includes('consulting')) return 'Consulting';
                if (industryLower.includes('consumer products')) return 'ConsumerProducts';
                if (industryLower.includes('education') || industryLower.includes('teaching') || industryLower.includes('school') || industryLower.includes('university')) return 'Education_Teaching_Administration';
                if (industryLower.includes('electronics')) return 'Electronics';
                if (industryLower.includes('energy') || industryLower.includes('utilities') || industryLower.includes('gas') || industryLower.includes('oil') || industryLower.includes('electric')) return 'Energy_Utilities_Gas_Oil_Electric';
                if (industryLower.includes('entertainment') || industryLower.includes('sports')) return 'Entertainment_Sports';
                if (industryLower.includes('environmental')) return 'Environmental';
                if (industryLower.includes('food') || industryLower.includes('beverage')) return 'Food_Beverages';
                if (industryLower.includes('government') || industryLower.includes('civil service')) return 'Government_CivilServices';
                if (industryLower.includes('healthcare') || industryLower.includes('health') || industryLower.includes('medical')) return 'Healthcare_HealthServices';
                if (industryLower.includes('hospitality') || industryLower.includes('tourism') || industryLower.includes('hotel')) return 'Hospitality_Tourism';
                if (industryLower.includes('human resources') || industryLower.includes('staffing') || industryLower.includes('hr')) return 'HumanResources_Staffing';
                if (industryLower.includes('hvac')) return 'HVAC';
                if (industryLower.includes('industrial') || industryLower.includes('materials')) return 'Industrial_Materials';
                if (industryLower.includes('insurance')) return 'Insurance';
                if (industryLower.includes('internet') || industryLower.includes('ecommerce') || industryLower.includes('e-commerce')) return 'Internet_ECommerce';
                if (industryLower.includes('law enforcement') || industryLower.includes('security')) return 'LawEnforcement_Security';
                if (industryLower.includes('legal')) return 'Legal';
                if (industryLower.includes('manufacturing')) return 'Manufacturing';
                if (industryLower.includes('merchandising')) return 'Merchandising';
                if (industryLower.includes('military')) return 'Military';
                if (industryLower.includes('nonprofit') || industryLower.includes('non-profit') || industryLower.includes('charity')) return 'NonProfit_Charity';
                if (industryLower.includes('office equipment')) return 'OfficeEquipment';
                if (industryLower.includes('packaging')) return 'Packaging';
                if (industryLower.includes('pharmaceutical') || industryLower.includes('pharma')) return 'Pharmaceutical';
                if (industryLower.includes('printing') || industryLower.includes('publishing')) return 'Printing_Publishing';
                if (industryLower.includes('public relations') || industryLower.includes('community relations')) return 'Public_CommunityRelations';
                if (industryLower.includes('real estate') || industryLower.includes('property')) return 'RealEstate_PropertyManagement';
                if (industryLower.includes('recreation')) return 'Recreation';
                if (industryLower.includes('restaurant') || industryLower.includes('food service')) return 'Restaurants_FoodService';
                if (industryLower.includes('retail')) return 'Retail';
                if (industryLower.includes('semiconductor')) return 'Semiconductor';
                if (industryLower.includes('telecommunications') || industryLower.includes('telecom')) return 'Telecommunications';
                if (industryLower.includes('training')) return 'Training_TrainingProducts';
                if (industryLower.includes('transportation') || industryLower.includes('shipping') || industryLower.includes('logistics')) return 'Transportation_Shipping';

                // Default fallback
                return 'General';
            };

            const mapEducation = (education: string) => {
                if (!education) return 'None';
                const eduLower = education.toLowerCase();
                if (eduLower.includes('high school') || eduLower.includes('diploma')) return 'HS_Diploma_Equivalent';
                if (eduLower.includes('associate')) return 'AssociatesDegree';
                if (eduLower.includes('bachelor') || eduLower.includes('undergraduate')) return 'BA_BS_Undergraduate';
                if (eduLower.includes('master')) return 'MastersDegree';
                if (eduLower.includes('phd') || eduLower.includes('doctorate')) return 'PhD';
                return 'None';
            };

            const mapJobType = (jobType: string) => {
                if (!jobType) return 'Full-Time';
                const jobTypeLower = jobType.toLowerCase();
                if (jobTypeLower.includes('part')) return 'Part-Time';
                if (jobTypeLower.includes('contract')) return 'Contract';
                if (jobTypeLower.includes('temporary')) return 'Temporary';
                if (jobTypeLower.includes('intern')) return 'Internship';
                return 'Full-Time';
            };

            const getSalaryInfo = (job: any) => {
                if (job.salaryYearMin || job.salaryYearMax) {
                    return {
                        salaryLow: job.salaryYearMin?.toString() || '',
                        salaryHigh: job.salaryYearMax?.toString() || '',
                        salaryType: 'Yearly Salary'
                    };
                } else if (job.salaryMonthMin || job.salaryMonthMax) {
                    return {
                        salaryLow: job.salaryMonthMin?.toString() || '',
                        salaryHigh: job.salaryMonthMax?.toString() || '',
                        salaryType: 'Monthly Salary'
                    };
                } else if (job.salaryHourMin || job.salaryHourMax) {
                    return {
                        salaryLow: job.salaryHourMin?.toString() || '',
                        salaryHigh: job.salaryHourMax?.toString() || '',
                        salaryType: 'Hourly Wage'
                    };
                }
                return {
                    salaryLow: '',
                    salaryHigh: '',
                    salaryType: 'Yearly Salary'
                };
            };

            const salaryInfo = getSalaryInfo(job);

            jobPayload = {
                requisitionName: job.id.toString(),
                title: job.title || 'Untitled Position',
                description: job.description || 'No description provided',
                companyName: company.name || 'Company Name',
                applyUrl: `${webAppsUrl}/job/${job.id}`,
                // applyUrl: `http://localhost:9000/job/${job.id}`,
                experience: mapExperience(job.experienceMin, job.experienceMax),
                jobFunction: mapJobFunction(job.functionalArea, job.position?.label),
                industry: mapIndustry(job.industry?.label),
                jobType: mapJobType(job.jobType),
                requirements: job.shortDescription || job.description || 'No specific requirements',
                duration: job.jobType?.toLowerCase().includes('contract') ? 'Contract' : 'Indefinite',
                travel: '0-10%',
                education: mapEducation(job.education),
                salaryLow: salaryInfo.salaryLow,
                salaryHigh: salaryInfo.salaryHigh,
                salaryType: salaryInfo.salaryType,
                contactName: `${user.firstname || ''} ${user.lastname || ''}`.trim() || 'HR Team',
                contactEmail: user.email || company.email || '',
                contactPhone: user.phone || company.phone || '',
                entryLevel: (job.experienceMin === 0 || job.experienceMax <= 1 || !job.experienceMin),
                easyApply: true,
                easyApplyType: 0,
                questionnaireWebhook: '',
                applicationdeliveryWebhook: '',
                locale: 'en-US',
                locations: job.locations?.map(location => ({
                    city: location.city || '',
                    state: location.state || '',
                    region: location.state || '',
                    country: 'US',
                    zip: '',
                    postalCode: '',
                    externalLocationId: location.id?.toString() || ''
                })) || [{
                    city: '',
                    state: '',
                    region: '',
                    country: 'US',
                    zip: '',
                    postalCode: '',
                    externalLocationId: ''
                }],
                created: new Date().toISOString(),
                updated: new Date().toISOString(),
                status: 'Open'
            };

            const response = await axios.post(
                'https://uat-partner-api.jobtarget.com/api/v1/job',
                jobPayload,
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                    validateStatus: (status) => status === 200 || status === 201,
                }
            );
            if (response.data?.jobId) {
                await this.jobModel.update(
                    {
                        jobTargetId: response.data.jobId.toString(),
                        sso_link: JSON.stringify(response.data?.sso || {}),
                    },
                    { where: { id: job.id } }
                );
            }
            return response.data;

        } catch (error) {
            Logger.error(`Error sending job to JobTarget: ${error.message}`, error.stack);

            if (error.response) {
                Logger.error(`JobTarget API Error Details:`, {
                    status: error.response.status,
                    statusText: error.response.statusText,
                    data: error.response.data,
                    headers: error.response.headers
                });
                Logger.error(`Payload that was sent:`, jobPayload);
            }

            return null;
        }
    }

    async getJobById(jobId: string, companyId: string) {
        // Step 1: Look up job in your database
        const job = await this.jobModel.findOne({
            where: { id: jobId, companyId },
        });

        if (!job) {
            Logger.log("Job not found");
        }

        if (!job.jobTargetId) {
            Logger.log("JobTarget ID not found for this job");
        }

        // Step 2: Fetch the user who authored the job
        const user = await this.userModel.findByPk(job.authorId);
        if (!user || !user.email) {
            Logger.log("Author of the job does not exist or has no email");
        }

        // Step 3: Fetch JobTarget token
        const token = await this.fetchJobTargetToken(user.email);

        // Step 4: Fetch job details from JobTarget API using jobTargetId
        try {
            const response = await axios.get(
                `https://uat-partner-api.jobtarget.com/api/v1/job/${job.jobTargetId}`,
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                    validateStatus: (status) => status === 200 || status === 404,
                }
            );

            if (response.status === 404) {
                Logger.log("JobTarget job not found");
            }

            const sso = response.data?.sso || {};

            // Step 5: Update sso_link in jobModel
            await this.jobModel.update(
                { sso_link: JSON.stringify(sso) },
                { where: { id: jobId } }
            );

            // Step 6: Return response
            return {
                jobTargetId: job.jobTargetId,
                sso,
            };
        } catch (error) {
            Logger.error("Failed to fetch job from JobTarget", error.response?.data || error.message);
            throw new InternalServerErrorException("Failed to retrieve job from JobTarget");
        }
    }

    async updateJob(job: any, existingJobData: any) {
        try {
            const companyId = job.companyId;
            const authorId = job.authorId;

            // Get integration credentials
            const integration = await this.getJobIntegrationDetails(companyId);
            if (!integration) {
                Logger.warn(`No JobTarget integration found for company ${companyId}`);
                return null;
            }

            // Get company & user
            const company = await this.companyModel.findByPk(companyId);
            const user = await this.userModel.findByPk(authorId);

            if (!company?.name || !user?.email) {
                Logger.warn(`Missing company name or user email for company ${companyId}`);
                return null;
            }

            // Get token
            const token = await this.fetchJobTargetToken(user.email);

            // Get the JobTarget jobId (from DB or passed in)
            const jobTargetId = job.jobTargetId || existingJobData?.jobId;
            if (!jobTargetId) {
                throw new Error('Missing JobTarget jobId for update');
            }

            // Build JobTarget payload (same as create)
            const jobPayload = {
                requisitionName: job.id.toString(),
                title: job.title,
                description: job.description,
                companyName: company.name,
                applyUrl: `https://yourdomain.com/apply/${job.id}`,
                locations: [
                    {
                        city: job.locations?.[0]?.city || '',
                        state: job.locations?.[0]?.state || '',
                        country: job.locations?.[0]?.country || 'US',
                        zip: job.locations?.[0]?.zip || 'KY16 9XW',
                    },
                ],
            };

            // PUT request to update existing job
            const response = await axios.put(
                `https://uat-partner-api.jobtarget.com/api/v1/job/${jobTargetId}`,
                jobPayload,
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                    validateStatus: (status) => status === 200 || status === 204,
                }
            );

            Logger.log(`Job updated in JobTarget successfully. Response:`, response.data || response.status);
            return response.data || { status: response.status };

        } catch (error) {
            Logger.error(`Error updating job in JobTarget: ${error.message}`, error.stack);
            return null;
        }
    }

    async deleteJob(job: any, jobTargetData: any) {
        try {
            const companyId = job.companyId;
            const authorId = job.authorId;

            if (!job || !job.companyId || !job.authorId) {
                Logger.error('Invalid job object passed to deleteJob:', job);
                return null;
            }

            // Get integration credentials (client ID/secret)
            const integration = await this.getJobIntegrationDetails(companyId);
            if (!integration) {
                Logger.warn(`No JobTarget integration found for company ${companyId}`);
                return null;
            }

            // Get company & user
            const company = await this.companyModel.findByPk(companyId);
            const user = await this.userModel.findByPk(authorId);

            if (!user?.email) {
                Logger.warn(`Missing user email for author ${authorId}`);
                return null;
            }

            // Fetch fresh token
            const token = await this.fetchJobTargetToken(user.email);

            const jobTargetId = jobTargetData?.jobId || job.jobTargetId;
            if (!jobTargetId) {
                Logger.warn(`No JobTarget jobId found to delete for job ${job.id}`);
                return null;
            }

            const response = await axios.delete(
                `https://uat-partner-api.jobtarget.com/api/v1/job/${jobTargetId}`,
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                    validateStatus: (status) => status === 200 || status === 204 || status === 404,
                }
            );

            if (response.status === 404) {
                Logger.warn(`JobTarget job ${jobTargetId} already deleted or not found`);
            } else {
                Logger.log(`JobTarget job ${jobTargetId} deleted successfully`);
            }

            Logger.error(`Deleted job from JobTarget`);
            return true;
        } catch (error) {
            Logger.error(`Error deleting job from JobTarget: ${error.message}`, error.stack);
            return null;
        }
    }

    async closeJob(job: any, jobTargetData?: any) {
        try {
            const companyId = job.companyId;
            const authorId = job.authorId;

            if (!job || !job.companyId || !job.authorId) {
                Logger.error('Invalid job object passed to closeJob:', job);
                return null;
            }

            const integration = await this.getJobIntegrationDetails(companyId);
            if (!integration) {
                Logger.warn(`No JobTarget integration found for company ${companyId}`);
                return null;
            }

            const user = await this.userModel.findByPk(authorId);
            if (!user?.email) {
                Logger.warn(`Missing user email for author ${authorId}`);
                return null;
            }

            const token = await this.fetchJobTargetToken(user.email);

            const jobTargetId = jobTargetData?.jobId || job.jobTargetId;
            if (!jobTargetId) {
                Logger.warn(`No JobTarget jobId found to close for job ${job.id}`);
                return null;
            }

            const response = await axios.post(
                `https://uat-partner-api.jobtarget.com/api/v1/job/${jobTargetId}/close`,
                {},
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                    validateStatus: (status) => status === 200 || status === 202 || status === 204 || status === 404,
                }
            );

            Logger.log(`JobTarget close job API response status: ${response.status}`, response.data);

            if (response.status === 404) {
                Logger.warn(`JobTarget job ${jobTargetId} not found or already closed`);
                return { success: false, message: 'Job not found or already closed' };
            } else if (response.status === 202) {
                Logger.log(`JobTarget job ${jobTargetId} queued for closing successfully`);
                return { success: true, message: 'Job queued for closing successfully', data: response.data };
            } else {
                Logger.log(`JobTarget job ${jobTargetId} closed successfully`);
                return { success: true, message: 'Job closed successfully', data: response.data };
            }

        } catch (error) {
            Logger.error(`Error closing job in JobTarget: ${error.message}`, error.stack);

            if (error.response?.status === 400) {
                Logger.warn(`JobTarget job may already be closed or invalid state: ${error.response?.data?.message || 'Bad request'}`);
                return { success: false, message: 'Job may already be closed or in invalid state' };
            }

            return { success: false, message: 'Failed to close job in JobTarget' };
        }
    }

    async updateApplicantStatus(applicantGuid: string, stage: string, userEmail: string): Promise<any> {
        try {
            if (!applicantGuid) {
                Logger.warn('No applicant GUID provided for JobTarget status update');
                return null;
            }

            if (!userEmail) {
                Logger.warn('No user email provided for JobTarget token generation');
                return null;
            }

            // Fetch fresh token
            const token = await this.fetchJobTargetToken(userEmail);

            const payload = {
                stage: stage
            };

            const response = await axios.put(
                `https://uat-partner-api.jobtarget.com/api/v1/applicant/${applicantGuid}`,
                payload,
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                    validateStatus: (status) => status === 200 || status === 204 || status === 404,
                }
            );

            if (response.status === 404) {
                Logger.warn(`JobTarget applicant not found for GUID: ${applicantGuid}`);
                return null;
            }

            Logger.log(`JobTarget applicant status updated successfully. GUID: ${applicantGuid}, Stage: ${stage}`);
            return response.data || { status: response.status };

        } catch (error) {
            Logger.error(`Error updating JobTarget applicant status: ${error.message}`, error.stack);
            return null;
        }
    }

}
