import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { TakeHomeSubmission } from './take-home-submission.model';
import { TakeHomeTask } from './take-home-task.model';
import { CodingAreaService } from '../coding-area/coding-area.service';
import { AssignmentService } from '../assignment/assignment.service';

export interface CreateTakeHomeSubmissionDto {
  jobId: number;
  candidateId: number;
  assessmentId: number;
  assignmentId?: number;
  duration?: string;
  language?: string;
  submittedAt?: Date;
}

export interface QuestionSubmissionData {
  questionId: number;
  questionName: string;
  finalCode: string;
  testCaseResults: TestCaseResult[];
  executionSummary?: any;
}

export interface TestCaseResult {
  testCaseId: number;
  input: string;
  expectedOutput: string;
  actualOutput?: string;
  passed: boolean;
  executionTime?: number;
  error?: string;
}

@Injectable()
export class TakeHomeSubmissionService {
  constructor(
    @InjectModel(TakeHomeSubmission)
    private takeHomeSubmissionModel: typeof TakeHomeSubmission,
    private codingAreaService: CodingAreaService,
    private assignmentService: AssignmentService,
  ) {}

  async create(dto: CreateTakeHomeSubmissionDto): Promise<TakeHomeSubmission> {
    try {
      // Check if submission already exists
      const existingSubmission = await this.takeHomeSubmissionModel.findOne({
        where: {
          jobId: dto.jobId,
          candidateId: dto.candidateId,
          assessmentId: dto.assessmentId,
        },
      });

      if (existingSubmission) {
        throw new BadRequestException('Submission already exists for this candidate and assessment');
      }

      // Get assignment data to retrieve take-home task details
      const assignment = await this.assignmentService.findAssignmentByJobId(dto.jobId, null);
      if (!assignment || !assignment.takeHomeTaskId) {
        throw new NotFoundException('Take-home task assignment not found');
      }

      // Collect final code from coding area if assignmentId is provided
      let questionSubmissions: QuestionSubmissionData[] = [];
      if (dto.assignmentId) {
        questionSubmissions = await this.collectFinalSubmissionData(dto.assignmentId, assignment.takeHomeTaskId);
      }

      // Create the submission record
      const submission = await this.takeHomeSubmissionModel.create({
        ...dto,
        questionSubmissions,
        submittedAt: dto.submittedAt || new Date(),
        testCaseSummary: this.generateTestCaseSummary(questionSubmissions),
      });

      return submission;
    } catch (error) {
      if (error instanceof BadRequestException || error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to create submission: ${error.message}`);
    }
  }

  async findByCandidate(candidateId: number, jobId: number): Promise<TakeHomeSubmission> {
    return this.takeHomeSubmissionModel.findOne({
      where: { candidateId, jobId },
      include: [{ model: TakeHomeTask, as: 'takeHomeTask' }],
    });
  }

  async findById(id: number): Promise<TakeHomeSubmission> {
    const submission = await this.takeHomeSubmissionModel.findByPk(id, {
      include: [{ model: TakeHomeTask, as: 'takeHomeTask' }],
    });

    if (!submission) {
      throw new NotFoundException('Submission not found');
    }

    return submission;
  }

  async updateFeedback(id: number, feedback: string, totalScore?: number): Promise<TakeHomeSubmission> {
    const submission = await this.findById(id);
    
    await submission.update({
      feedback,
      ...(totalScore !== undefined && { totalScore }),
    });

    return submission;
  }

  private async collectFinalSubmissionData(assignmentId: number, takeHomeTaskId: number): Promise<QuestionSubmissionData[]> {
    try {
      // Get the latest coding area data for this assignment
      const codingAreaData = await this.codingAreaService.findAllByAssignmentId(assignmentId);
      
      if (!codingAreaData || codingAreaData.length === 0) {
        return [];
      }

      // Get the final state (latest entry) for each question
      const latestEntry = codingAreaData[codingAreaData.length - 1];
      
      // Parse the editor state to extract code and test results
      const editorState = JSON.parse(latestEntry.editorState);
      
      // This is a simplified version - you may need to adjust based on your actual editor state structure
      const questionSubmissions: QuestionSubmissionData[] = [];
      
      // If the editor state contains multiple questions/files
      if (editorState.questions) {
        editorState.questions.forEach((question: any, index: number) => {
          questionSubmissions.push({
            questionId: question.id || index,
            questionName: question.name || `Question ${index + 1}`,
            finalCode: question.code || question.solution || '',
            testCaseResults: question.testResults || [],
            executionSummary: question.executionSummary,
          });
        });
      } else {
        // Single question/file scenario
        questionSubmissions.push({
          questionId: 1,
          questionName: 'Main Solution',
          finalCode: editorState.code || editorState.content || '',
          testCaseResults: editorState.testResults || [],
          executionSummary: editorState.executionSummary,
        });
      }

      return questionSubmissions;
    } catch (error) {
      console.error('Error collecting submission data:', error);
      return [];
    }
  }

  private generateTestCaseSummary(questionSubmissions: QuestionSubmissionData[]): any {
    const summary = {
      totalQuestions: questionSubmissions.length,
      totalTestCases: 0,
      passedTestCases: 0,
      failedTestCases: 0,
      successRate: 0,
      questionResults: [],
    };

    questionSubmissions.forEach((question) => {
      const questionSummary = {
        questionId: question.questionId,
        questionName: question.questionName,
        totalTests: question.testCaseResults.length,
        passedTests: question.testCaseResults.filter(tc => tc.passed).length,
        failedTests: question.testCaseResults.filter(tc => !tc.passed).length,
      };

      summary.totalTestCases += questionSummary.totalTests;
      summary.passedTestCases += questionSummary.passedTests;
      summary.failedTestCases += questionSummary.failedTests;
      summary.questionResults.push(questionSummary);
    });

    summary.successRate = summary.totalTestCases > 0 
      ? Math.round((summary.passedTestCases / summary.totalTestCases) * 100) 
      : 0;

    return summary;
  }

  async findAll(companyId?: number): Promise<TakeHomeSubmission[]> {
    const whereClause = companyId ? { '$takeHomeTask.companyId$': companyId } : {};
    
    return this.takeHomeSubmissionModel.findAll({
      where: whereClause,
      include: [{ model: TakeHomeTask, as: 'takeHomeTask' }],
      order: [['submittedAt', 'DESC']],
    });
  }
}
